package com.minjisuper.inquiry.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.model.PublishRequest;
import software.amazon.awssdk.services.sns.model.PublishResponse;
import software.amazon.awssdk.services.sns.model.SnsException;

@Service
@Profile("aws") // aws 프로파일일 때만 활성화
public class AwsSmsService implements SmsService {
    
    private static final Logger logger = LoggerFactory.getLogger(AwsSmsService.class);
    
    @Value("${aws.accessKey}")
    private String accessKey;
    
    @Value("${aws.secretKey}")
    private String secretKey;
    
    @Value("${aws.region:ap-northeast-2}") // 서울 리전 기본값
    private String region;
    
    @Value("${aws.sns.senderName:MinjiSuper}")
    private String senderName;
    
    private SnsClient snsClient;
    
    @Override
    public boolean sendSms(String recipientPhone, String message) {
        if (!isServiceAvailable()) {
            logger.error("AWS SNS 서비스 설정이 올바르지 않습니다.");
            return false;
        }
        
        try {
            logger.info("AWS SNS SMS 발송 시작 - 수신자: {}, 메시지 길이: {}", 
                       maskPhoneNumber(recipientPhone), message.length());
            
            // SNS 클라이언트 초기화 (지연 초기화)
            if (snsClient == null) {
                initializeSnsClient();
            }
            
            // 국제 형식으로 전화번호 변환 (한국: +82)
            String formattedPhone = formatPhoneNumber(recipientPhone);
            
            PublishRequest request = PublishRequest.builder()
                    .phoneNumber(formattedPhone)
                    .message(message)
                    .build();
            
            PublishResponse response = snsClient.publish(request);
            
            logger.info("AWS SNS SMS 발송 성공 - MessageId: {}", response.messageId());
            return true;
            
        } catch (SnsException e) {
            logger.error("AWS SNS SMS 발송 실패 - 오류 코드: {}, 메시지: {}", 
                        e.awsErrorDetails().errorCode(), e.awsErrorDetails().errorMessage());
            return false;
        } catch (Exception e) {
            logger.error("AWS SNS SMS 발송 중 예외 발생: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean isServiceAvailable() {
        boolean isConfigured = accessKey != null && !accessKey.equals("YOUR_AWS_ACCESS_KEY") &&
                              secretKey != null && !secretKey.equals("YOUR_AWS_SECRET_KEY") &&
                              region != null && !region.isEmpty();
        
        if (!isConfigured) {
            logger.warn("AWS SNS 서비스 설정이 완료되지 않았습니다. application.properties를 확인하세요.");
        }
        
        return isConfigured;
    }
    
    /**
     * SNS 클라이언트를 초기화합니다.
     */
    private void initializeSnsClient() {
        try {
            AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(accessKey, secretKey);
            
            snsClient = SnsClient.builder()
                    .region(Region.of(region))
                    .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                    .build();
                    
            logger.info("AWS SNS 클라이언트 초기화 완료 - 리전: {}", region);
            
        } catch (Exception e) {
            logger.error("AWS SNS 클라이언트 초기화 실패: {}", e.getMessage(), e);
            throw new RuntimeException("AWS SNS 클라이언트 초기화 실패", e);
        }
    }
    
    /**
     * 전화번호를 국제 형식으로 변환합니다.
     * 010xxxxxxxx -> +8210xxxxxxxx
     */
    private String formatPhoneNumber(String phoneNumber) {
        if (phoneNumber.startsWith("010")) {
            return "+82" + phoneNumber.substring(1);
        }
        return phoneNumber;
    }
    
    /**
     * 전화번호를 마스킹합니다.
     */
    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber.length() >= 11) {
            return phoneNumber.replaceAll("(\\d{3})(\\d{4})(\\d{4})", "$1-****-$3");
        }
        return phoneNumber;
    }
}
