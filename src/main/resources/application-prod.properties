# ???? ??

# ?? ?? (????)
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web=WARN
logging.level.com.minjisuper=INFO
logging.level.org.hibernate.SQL=WARN

# ?? ?? ??
server.error.include-message=never
server.error.include-binding-errors=never
server.error.include-stacktrace=never
server.error.include-exception=false

# JPA ?? (????)
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# H2 ?? ???? (????)
spring.h2.console.enabled=false

# ??? ???? SMS API ?? (????)
# ?? NCP ?? ??? ?? ??
ncloud.accessKey=${NCP_ACCESS_KEY:YOUR_ACCESS_KEY}
ncloud.secretKey=${NCP_SECRET_KEY:YOUR_SECRET_KEY}
ncloud.serviceId=${NCP_SERVICE_ID:YOUR_SERVICE_ID}
ncloud.senderPhone=${NCP_SENDER_PHONE:01012345678}
ncloud.recipientPhone=${NCP_RECIPIENT_PHONE:01098765432}
