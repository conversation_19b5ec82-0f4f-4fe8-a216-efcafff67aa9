# 데이터베이스 스키마 가이드

## 📋 테이블 구조

### `inquiry` 테이블 - 창업 문의 정보

| 컬럼명 | 타입 | 제약조건 | 설명 |
|--------|------|----------|------|
| `id` | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 문의 ID (자동증가) |
| `name` | VARCHAR(50) | NOT NULL | 이름 |
| `phone` | VARCHAR(11) | NOT NULL, PATTERN | 연락처 (하이픈 없이, *********** 형식) |
| `email` | VARCHAR(100) | NOT NULL, EMAIL | 이메일 |
| `business_type` | VARCHAR(10) | NOT NULL, CHECK | 창업유형 ('창업' 또는 '업종변경') |
| `province` | VARCHAR(20) | NOT NULL | 창업희망지역 - 시/도 |
| `city` | VARCHAR(30) | NOT NULL | 창업희망지역 - 시/군/구 |
| `agreed` | BOOLEAN | NOT NULL | 약관 동의 여부 |
| `created_at` | TIMESTAMP | NOT NULL, DEFAULT | 접수시간 |

## 🗂 SQL 파일 목록

### 1. `sql.sql` (MySQL/MariaDB)
- **용도**: MySQL 또는 MariaDB 운영환경
- **특징**: 
  - CHECK 제약조건 포함
  - REGEXP를 이용한 유효성 검사
  - 한글 코멘트 지원

### 2. `sql-h2.sql` (H2 Database)
- **용도**: 개발환경 (Spring Boot 기본 설정)
- **특징**:
  - H2 문법에 맞게 최적화
  - IDENTITY 컬럼 사용
  - 제약조건 단순화

### 3. `sql-postgresql.sql` (PostgreSQL)
- **용도**: PostgreSQL 운영환경
- **특징**:
  - BIGSERIAL 사용
  - 정규표현식 연산자 (~) 사용
  - 상세한 코멘트 포함

## 🔧 사용 방법

### 개발환경 (H2)
```bash
# Spring Boot 애플리케이션 실행 시 자동으로 테이블 생성됨
# 수동으로 실행하려면:
# H2 콘솔 접속 후 sql-h2.sql 실행
```

### MySQL/MariaDB 운영환경
```sql
-- 데이터베이스 생성
CREATE DATABASE minjisuper_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE minjisuper_db;

-- 테이블 생성
SOURCE sql.sql;
```

### PostgreSQL 운영환경
```sql
-- 데이터베이스 생성
CREATE DATABASE minjisuper_db;
\c minjisuper_db;

-- 테이블 생성
\i sql-postgresql.sql;
```

## 📊 샘플 데이터

모든 SQL 파일에는 테스트용 샘플 데이터가 포함되어 있습니다:

```sql
INSERT INTO inquiry (name, phone, email, business_type, province, city, agreed) VALUES
('김창업', '***********', '<EMAIL>', '창업', '서울특별시', '강남구', true),
('이업종', '***********', '<EMAIL>', '업종변경', '경기도', '성남시', true),
('박사업', '***********', '<EMAIL>', '창업', '부산광역시', '해운대구', true);
```

## 🔍 유용한 쿼리

### 전체 문의 조회
```sql
SELECT * FROM inquiry ORDER BY created_at DESC;
```

### 창업유형별 통계
```sql
SELECT business_type, COUNT(*) as count 
FROM inquiry 
GROUP BY business_type;
```

### 지역별 통계
```sql
SELECT province, COUNT(*) as count 
FROM inquiry 
GROUP BY province 
ORDER BY count DESC;
```

### 최근 문의 조회 (최근 7일)
```sql
SELECT * FROM inquiry 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY created_at DESC;
```

## 🚀 Spring Boot 설정

### application.properties 예시

#### H2 (개발환경)
```properties
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.jpa.hibernate.ddl-auto=create-drop
```

#### MySQL (운영환경)
```properties
spring.datasource.url=***********************************************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=validate
```

#### PostgreSQL (운영환경)
```properties
spring.datasource.url=**********************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.hibernate.ddl-auto=validate
```

## 📝 주의사항

1. **운영환경에서는 `ddl-auto=validate` 사용 권장**
2. **샘플 데이터는 운영환경에서 제거**
3. **데이터베이스 백업 정책 수립 필요**
4. **인덱스 성능 모니터링 권장**
