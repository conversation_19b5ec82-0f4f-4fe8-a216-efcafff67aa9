-- 창업 문의 테이블 (H2 Database 버전)
CREATE TABLE inquiry (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    phone VARCHAR(11) NOT NULL,
    email VARCHAR(100) NOT NULL,
    business_type VARCHAR(10) NOT NULL,
    province VARCHAR(20) NOT NULL,
    city VARCHAR(30) NOT NULL,
    agreed BOOLEAN NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 인덱스 생성
CREATE INDEX idx_inquiry_created_at ON inquiry(created_at);
CREATE INDEX idx_inquiry_business_type ON inquiry(business_type);
CREATE INDEX idx_inquiry_province ON inquiry(province);

-- 샘플 데이터 (테스트용)
INSERT INTO inquiry (name, phone, email, business_type, province, city, agreed, created_at) VALUES
('김창업', '***********', '<EMAIL>', '창업', '서울특별시', '강남구', true, CURRENT_TIMESTAMP),
('이업종', '***********', '<EMAIL>', '업종변경', '경기도', '성남시', true, CURRENT_TIMESTAMP),
('박사업', '***********', '<EMAIL>', '창업', '부산광역시', '해운대구', true, CURRENT_TIMESTAMP);

-- 테이블 구조 확인 쿼리
-- SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'INQUIRY';

-- 데이터 조회 쿼리
-- SELECT * FROM inquiry ORDER BY created_at DESC;
