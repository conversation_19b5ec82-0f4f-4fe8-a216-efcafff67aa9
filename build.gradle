plugins {
	id 'java'
	id 'org.springframework.boot' version '3.4.0'
	id 'io.spring.dependency-management' version '1.1.6'
}

group = 'com.MinjiSuper'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-websocket'
	implementation 'org.projectlombok:lombok:1.18.28'
	compileOnly 'org.projectlombok:lombok'
	developmentOnly 'org.springframework.boot:spring-boot-devtools'
	annotationProcessor 'org.projectlombok:lombok'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework.security:spring-security-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'

	// H2 데이터베이스 (개발용)
	runtimeOnly 'com.h2database:h2'

	// AWS SDK BOM (Bill of Materials) - 버전 관리
	implementation platform('software.amazon.awssdk:bom:2.21.29')

	// AWS SNS (SMS 발송용)
	implementation 'software.amazon.awssdk:sns'
	implementation 'software.amazon.awssdk:auth'

	// 네이버 클라우드 SMS API 관련 (JSON 데이터 변환용) - 기존 유지
	implementation 'com.fasterxml.jackson.core:jackson-databind'

	// RESTful API 호출을 위한 HTTP 클라이언트 (선택 사항)
	implementation 'org.apache.httpcomponents.client5:httpclient5'
}

tasks.named('test') {
	useJUnitPlatform()
}
