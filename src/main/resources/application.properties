spring.application.name=MinjiSuper-BE

# ?????? ?? (H2 ???? ?????? - ???)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# H2 ?? ??? (???)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA ??
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# SQL ??? ??
spring.sql.init.mode=never

# ?? ??
server.error.include-message=never
server.error.include-binding-errors=never
server.error.include-stacktrace=never
server.error.include-exception=false

# ?? ??
logging.level.org.springframework.security=INFO
logging.level.org.springframework.web=INFO
logging.level.com.minjisuper=DEBUG

# Active Profile Setting (dev: development, prod: AWS production)
spring.profiles.active=dev

# AWS SNS SMS API Settings (Production Environment)
aws.accessKey=${AWS_ACCESS_KEY:YOUR_AWS_ACCESS_KEY}
aws.secretKey=${AWS_SECRET_KEY:YOUR_AWS_SECRET_KEY}
aws.region=${AWS_REGION:ap-northeast-2}
aws.sns.senderName=${AWS_SNS_SENDER_NAME:MinjiSuper}

# SMS Recipient Settings
sms.recipientPhone=${SMS_RECIPIENT_PHONE:01098765432}
