# AWS SNS SMS 서비스 설정 가이드

## 1. AWS 계정 및 IAM 설정

### 1.1 AWS 계정 생성
1. [AWS 콘솔](https://aws.amazon.com/) 접속
2. 계정 생성 및 결제 정보 등록
3. 루트 계정으로 로그인

### 1.2 IAM 사용자 생성 (권장)
1. AWS 콘솔 → `IAM` → `사용자` → `사용자 생성`
2. 사용자 이름: `minjisuper-sms`
3. 액세스 유형: `프로그래밍 방식 액세스` 선택
4. 권한 설정: `기존 정책 직접 연결`
5. 정책 검색: `AmazonSNSFullAccess` 선택
6. 사용자 생성 완료 후 **Access Key ID**와 **Secret Access Key** 저장

### 1.3 최소 권한 정책 (보안 강화)
더 안전한 방법으로 최소 권한만 부여:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "sns:Publish"
            ],
            "Resource": "*"
        }
    ]
}
```

## 2. AWS SNS 설정

### 2.1 SMS 샌드박스 해제 (중요!)
1. AWS 콘솔 → `Simple Notification Service (SNS)`
2. 좌측 메뉴 → `Mobile` → `Text messaging (SMS)`
3. `SMS preferences` → `Edit`
4. **Sandbox mode 해제 요청** (프로덕션 사용을 위해 필수)
5. 사용 사례 설명 및 승인 대기 (보통 24시간 내)

### 2.2 SMS 설정 확인
1. `Text messaging (SMS)` → `SMS preferences`
2. Default message type: `Transactional` (중요한 알림용)
3. Default sender ID: `MinjiSuper` (선택사항)
4. Delivery status logging: 활성화 권장

## 3. 애플리케이션 설정

### 3.1 환경변수 설정
```bash
# AWS 인증 정보
export AWS_ACCESS_KEY="AKIA..."
export AWS_SECRET_KEY="..."
export AWS_REGION="ap-northeast-2"  # 서울 리전
export AWS_SNS_SENDER_NAME="MinjiSuper"
export SMS_RECIPIENT_PHONE="01098765432"
```

### 3.2 프로파일별 실행
```bash
# AWS 환경으로 실행
SPRING_PROFILES_ACTIVE=aws ./gradlew bootRun

# 또는 JAR 실행 시
java -jar -Dspring.profiles.active=aws MinjiSuper-BE.jar
```

## 4. 비용 정보

### 4.1 SMS 요금 (한국 기준)
- **국내 SMS**: 약 $0.00645 per message (약 8.5원)
- **국제 SMS**: 국가별 상이 ($0.01 ~ $0.15)

### 4.2 비용 절약 팁
- Transactional 메시지만 사용 (Promotional보다 저렴)
- 불필요한 발송 최소화
- CloudWatch로 사용량 모니터링

## 5. 프로파일 비교

| 프로파일 | SMS 서비스 | 용도 | 비용 |
|---------|-----------|------|------|
| `dev` | FakeNaverSmsService | 개발/테스트 | 무료 |
| `prod` | NaverSmsService | NCP 운영 | NCP 요금 |
| `aws` | AwsSmsService | AWS 운영 | AWS 요금 |

## 6. 장단점 비교

### AWS SNS 장점
- ✅ **글로벌 서비스**: 전 세계 SMS 발송 가능
- ✅ **AWS 생태계**: S3, EC2 등과 통합 용이
- ✅ **확장성**: 대용량 발송 지원
- ✅ **모니터링**: CloudWatch 연동
- ✅ **안정성**: 99.9% SLA 보장

### AWS SNS 단점
- ❌ **샌드박스**: 초기 설정 복잡
- ❌ **국제 요금**: 해외 발송 시 비용 증가
- ❌ **한글 지원**: 일부 제약 있을 수 있음

### NCP SMS 장점
- ✅ **한국 특화**: 국내 서비스에 최적화
- ✅ **한글 완벽 지원**: 인코딩 문제 없음
- ✅ **국내 요금**: 상대적으로 저렴
- ✅ **즉시 사용**: 샌드박스 없음

## 7. 권장사항

### 7.1 국내 서비스인 경우
- **NCP SMS 권장**: 한국 사용자 대상, 한글 메시지
- 비용 효율적이고 설정 간단

### 7.2 글로벌 서비스인 경우
- **AWS SNS 권장**: 다국가 서비스, 확장성 필요
- AWS 인프라 사용 시 통합 관리 용이

### 7.3 하이브리드 접근
```bash
# 개발: Mock 서비스
SPRING_PROFILES_ACTIVE=dev

# 국내 운영: NCP
SPRING_PROFILES_ACTIVE=prod

# 글로벌 운영: AWS
SPRING_PROFILES_ACTIVE=aws
```

## 8. 테스트 방법

### 8.1 AWS 환경 테스트
```bash
# 1. AWS 프로파일로 실행
SPRING_PROFILES_ACTIVE=aws ./gradlew bootRun

# 2. SMS 상태 확인
curl http://localhost:8080/api/inquiry/sms-status

# 3. 실제 문의 접수 테스트
curl -X POST http://localhost:8080/api/inquiry \
  -H "Content-Type: application/json" \
  -d '{
    "name": "AWS테스트",
    "phone": "***********",
    "email": "<EMAIL>",
    "businessType": "창업",
    "province": "서울특별시",
    "city": "강남구",
    "agreed": true
  }'
```

## 9. 문제 해결

### 9.1 자주 발생하는 오류
- **InvalidParameterException**: 전화번호 형식 오류
- **AuthorizationException**: IAM 권한 부족
- **ThrottledException**: 발송 한도 초과
- **EndpointDisabledException**: 샌드박스 모드

### 9.2 로그 확인
```bash
# AWS SDK 로그 활성화
logging.level.software.amazon.awssdk=DEBUG
```

## 10. 보안 고려사항

### 10.1 AWS 자격 증명 보안
- IAM 역할 사용 권장 (EC2에서 실행 시)
- 환경변수로 키 관리
- 정기적인 키 로테이션

### 10.2 VPC 설정 (선택사항)
- VPC 엔드포인트 사용으로 트래픽 보안 강화
- 프라이빗 서브넷에서 SNS 접근
