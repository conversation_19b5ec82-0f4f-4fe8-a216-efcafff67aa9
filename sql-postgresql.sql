-- 창업 문의 테이블 (PostgreSQL 버전)
CREATE TABLE inquiry (
    id BIGSERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    phone VARCHAR(11) NOT NULL,
    email VARCHAR(100) NOT NULL,
    business_type VARCHAR(10) NOT NULL,
    province VARCHAR(20) NOT NULL,
    city VARCHAR(30) NOT NULL,
    agreed BOOLEAN NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 제약조건 추가
ALTER TABLE inquiry ADD CONSTRAINT chk_business_type 
    CHECK (business_type IN ('창업', '업종변경'));

ALTER TABLE inquiry ADD CONSTRAINT chk_phone_format 
    CHECK (phone ~ '^010[0-9]{8}$');

ALTER TABLE inquiry ADD CONSTRAINT chk_email_format 
    CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- 인덱스 생성
CREATE INDEX idx_inquiry_created_at ON inquiry(created_at);
CREATE INDEX idx_inquiry_business_type ON inquiry(business_type);
CREATE INDEX idx_inquiry_province ON inquiry(province);

-- 테이블에 코멘트 추가
COMMENT ON TABLE inquiry IS '창업 문의 테이블';
COMMENT ON COLUMN inquiry.id IS '문의 ID (자동증가)';
COMMENT ON COLUMN inquiry.name IS '이름';
COMMENT ON COLUMN inquiry.phone IS '연락처 (하이픈 없이, *********** 형식)';
COMMENT ON COLUMN inquiry.email IS '이메일';
COMMENT ON COLUMN inquiry.business_type IS '창업유형 (창업 또는 업종변경)';
COMMENT ON COLUMN inquiry.province IS '창업희망지역 - 시/도';
COMMENT ON COLUMN inquiry.city IS '창업희망지역 - 시/군/구';
COMMENT ON COLUMN inquiry.agreed IS '약관 동의 여부';
COMMENT ON COLUMN inquiry.created_at IS '접수시간';

-- 샘플 데이터 (테스트용)
INSERT INTO inquiry (name, phone, email, business_type, province, city, agreed, created_at) VALUES
('김창업', '***********', '<EMAIL>', '창업', '서울특별시', '강남구', true, CURRENT_TIMESTAMP),
('이업종', '***********', '<EMAIL>', '업종변경', '경기도', '성남시', true, CURRENT_TIMESTAMP),
('박사업', '***********', '<EMAIL>', '창업', '부산광역시', '해운대구', true, CURRENT_TIMESTAMP);

-- 유용한 쿼리들
-- 테이블 구조 확인
-- \d inquiry

-- 데이터 조회
-- SELECT * FROM inquiry ORDER BY created_at DESC;

-- 창업유형별 통계
-- SELECT business_type, COUNT(*) as count FROM inquiry GROUP BY business_type;

-- 지역별 통계
-- SELECT province, COUNT(*) as count FROM inquiry GROUP BY province ORDER BY count DESC;
