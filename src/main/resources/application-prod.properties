# AWS Production Environment Settings

# Logging Settings (Production)
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web=WARN
logging.level.com.minjisuper=INFO
logging.level.software.amazon.awssdk=INFO

# Security Settings
server.error.include-message=never
server.error.include-binding-errors=never
server.error.include-stacktrace=never
server.error.include-exception=false

# JPA Settings (Production)
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# H2 Console Disabled (Production)
spring.h2.console.enabled=false

# AWS SNS SMS Settings (Production)
aws.accessKey=${AWS_ACCESS_KEY:YOUR_AWS_ACCESS_KEY}
aws.secretKey=${AWS_SECRET_KEY:YOUR_AWS_SECRET_KEY}
aws.region=${AWS_REGION:ap-northeast-2}
aws.sns.senderName=${AWS_SNS_SENDER_NAME:MinjiSuper}

# SMS Recipient Settings
sms.recipientPhone=${SMS_RECIPIENT_PHONE:01098765432}
