spring.application.name=<PERSON><PERSON><PERSON>uper-BE

# ?????? ?? (H2 ???? ?????? - ???)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# H2 ?? ??? (???)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA ??
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# SQL ??? ??
spring.sql.init.mode=never

# ??? ???? SMS API ??
ncloud.accessKey=YOUR_ACCESS_KEY
ncloud.secretKey=YOUR_SECRET_KEY
ncloud.serviceId=YOUR_SERVICE_ID
ncloud.senderPhone=01012345678
